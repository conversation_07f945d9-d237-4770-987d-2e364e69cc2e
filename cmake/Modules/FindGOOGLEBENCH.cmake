# - Try to find <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
#
# Once done this will define
#
#  GOO<PERSON><PERSON><PERSON><PERSON><PERSON>_FOUND - system has GOO<PERSON><PERSON><PERSON>NCH
#  GOOG<PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIRS - the GOOGLEBENCH include directory
#  GOO<PERSON><PERSON><PERSON><PERSON>H_LIBRARIES - Link these to use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
#

find_path(
    GOOG<PERSON><PERSON><PERSON><PERSON>_INCLUDE_DIRS
    NAMES benchmark/benchmark.h
    HINTS ${GOOG<PERSON><PERSON>NCH_INCLUDE_DIRS}
)

find_library(
    GOOGLEBENCH_LIBRARIES
    NAMES benchmark
    HINTS ${GOOG<PERSON><PERSON>NCH_LIBRARY_DIRS}
)

include(FindPackageHandleStandardArgs)
FIND_PACKAGE_HANDLE_STANDARD_ARGS(
    GOOGLEBENCH
    DEFAULT_MSG
    GOOG<PERSON><PERSON>NCH_LIBRARIES
    GOOG<PERSON><PERSON>NCH_INCLUDE_DIRS
)

if(GOOGLEBENCH_FOUND)
  mark_as_advanced(GOOG<PERSON>BENCH_LIBRARIES GOOGLE<PERSON><PERSON><PERSON>_INCLUDE_DIRS)
endif()

