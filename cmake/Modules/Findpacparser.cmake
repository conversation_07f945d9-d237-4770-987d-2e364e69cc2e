# -*- cmake -*-

# - Find pacparser
# Find the pacparser includes and library
# This module defines
#  PACPARSER_INCLUDE_DIR, where to find pacparser.h
#  PACPARSER_LIBRARIES, the libraries needed to use pacparser
#  PACPARSER_FOUND, If false, do not try to use pacparser
# also defined, but not for general use are
#  PACPARSER_LIBRARY, where to find the pacparser library.

FIND_PATH(PACPARSER_INCLUDE_DIR pacparser.h
/usr/local/include
/usr/include
)

SET(PACPARSER_NAMES ${PACPARSER_NAMES} pacparser)
FIND_LIBRARY(PACPARSER_LIBRARY
  NAMES ${PACPARSER_NAMES}
  PATHS /usr/lib /usr/local/lib
  )

IF (PACPARSER_LIBRARY AND PACPARSER_INCLUDE_DIR)
    SET(PACPARSER_LIBRARIES ${PACPARSER_LIBRARY})
    SET(PACPARSER_FOUND "YES")
ELSE (PACPARSER_LIBRARY AND PACPARSER_INCLUDE_DIR)
  SET(PACPARSER_FOUND "NO")
ENDIF (PACPARSER_LIBRARY AND PACPARSER_INCLUDE_DIR)


IF (PACPARSER_FOUND)
   IF (NOT PACPARSER_FIND_QUIETLY)
      MESSAGE(STATUS "Found pacparser: ${PACPARSER_LIBRARIES}")
   ENDIF (NOT PACPARSER_FIND_QUIETLY)
ELSE (PACPARSER_FOUND)
   IF (PACPARSER_FIND_REQUIRED)
      MESSAGE(FATAL_ERROR "Could not find pacparser library")
   ENDIF (PACPARSER_FIND_REQUIRED)
ENDIF (PACPARSER_FOUND)

# Deprecated declarations.
SET (NATIVE_PACPARSER_INCLUDE_PATH ${PACPARSER_INCLUDE_DIR} )
GET_FILENAME_COMPONENT (NATIVE_PACPARSER_LIB_PATH ${PACPARSER_LIBRARY} PATH)

MARK_AS_ADVANCED(
  PACPARSER_LIBRARY
  PACPARSER_INCLUDE_DIR
  )
