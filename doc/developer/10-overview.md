# Quickstart Guide for Developers

## Introduction

The following documents in this folder are a quickstart guide for developers. 
While most of it is explained in detail in the [documentation](https://cvmfs.readthedocs.io/en/stable/), this quickstart guide aims to provide complete working code examples for developers with hints about best practices: dos and don'ts.

## Content
- [Codestyle and Git Contribution](20-codestyle-and-git-contribution.md)
- [Building from Source](30-building-from-source.md)
- Setup
  - [Setup CVMFS Server](40-setup-cvmfs-server.md)
  - [Setup CVMFS Gateway](41-setup-cvmfs-gateway.md)
  - [Setup CVMFS Server with S3 Storage](42-setup-s3storage.md)
  - [Setup CVMFS Client](50-setup-cvmfs-client.md)
- [Debugging and Testing](60-debugging-and-testing.md)
- Coding Templates
  - [Execute Work Every X Sec (in Separate Thread)](70-coding_template_timed-loop-thread.md)

