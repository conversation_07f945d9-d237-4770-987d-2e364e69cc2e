#!/bin/bash

echo "WARNING: This is a mock command for testing of ducc that does noops and simple copies on a normal folder"

# Since /cvmfs + reponame  and /var/spool/cvmfs is hardcoded,
# CVMFS_TEST_REPO is expected to look like ../../../tmp/testrepofolder


if [ "$1" == "list" ]; then
  echo "testunpacked.cern.ch"
  echo "..$CVMFS_TEST_REPO"
fi

# Template transaction (cvmfs_server transaction -T /from:/to)
# just do a copy
if [ "$1" == "transaction" ]; then
  arg2=$(echo $@ | cut -d ' ' -f 2)
  if [ "$arg2" == "-T" ]; then
    arg3=$(echo $@ | cut -d ' ' -f 3)
    from=$(echo $arg3 | cut -d '=' -f 1)
    to=$(echo $arg3 | cut -d '=' -f 2)
    echo "template transaction from $CVMFS_TEST_REPO/$from to $CVMFS_TEST_REPO/$to" 
    mkdir -p $CVMFS_TEST_REPO/$to
    cp -r $CVMFS_TEST_REPO/$from/* $CVMFS_TEST_REPO/$to
  fi
fi

# cvmfs_server publish
# needs to support sneaky transactions
# just apply changes
# the upperdir would usually be in /var/spool/cvmfs
# We leave it in CVMFS_TEST_REPO for convenience   
if [ "$1" == "publish" ]; then
  # needs sudo to work for opaque directories
  mockcvmfs_overlay_helper.sh $CVMFS_TEST_REPO/scratch/current $CVMFS_TEST_REPO
fi

# cvmfs_server ingest --delete
# just rm
if [ "$1" == "ingest" ]; then
  arg2=$(echo $@ | cut -d ' ' -f 2)
  if [ "$arg2" == "--delete" ]; then
    echo "deleting /$CVMFS_TEST_REPO/$3"
    rm -rf /$CVMFS_TEST_REPO/$3
  fi
fi

# cvmfs_server ingest --catalog -t - -b <dir>
arg2=$(echo $@ | cut -d ' ' -f 2)
arg3=$(echo $@ | cut -d ' ' -f 3)
arg4=$(echo $@ | cut -d ' ' -f 4)
arg5=$(echo $@ | cut -d ' ' -f 5)
if [ "$1" == "ingest" ]; then
  if [ "$arg2" == "--catalog" ]; then
    if [ "$arg3" == "-t" ]; then
      if [ "$arg4" == "-" ]; then
        if [ "$arg5" == "-b" ]; then
          mkdir -p /$CVMFS_TEST_REPO/$6
          cat - > /tmp/tmptar.tar
          tar xf /tmp/tmptar.tar -C /$CVMFS_TEST_REPO/$6
        fi
      fi
    fi
  fi
fi
