__x86.get_pc_thunk.ax
__x86.get_pc_thunk.bx
__x86.get_pc_thunk.cx
__x86.get_pc_thunk.di
__x86.get_pc_thunk.dx
__x86.get_pc_thunk.si
__i686.get_pc_thunk.bx
__i686.get_pc_thunk.cx
cvmcache_hash_cmp
cvmcache_hash_print
cvmcache_init_global
cvmcache_cleanup_global
cvmcache_is_supervised
cvmcache_init
cvmcache_listen
cvmcache_process_requests
cvmcache_ask_detach
cvmcache_wait_for
cvmcache_terminate
cvmcache_max_object_size
cvmcache_spawn_watchdog
cvmcache_terminate_watchdog
cvmcache_options_init
cvmcache_options_fini
cvmcache_options_set
cvmcache_options_parse
cvmcache_options_unset
cvmcache_options_get
cvmcache_options_dump
cvmcache_options_free
