/**
 * This file is part of the CernVM File System.
 */
#ifndef CVMFS_SHRINKWRAP_POSIX_HELPERS_H_
#define CVMFS_SHRINKWRAP_POSIX_HELPERS_H_

#include <sys/stat.h>
#include <sys/types.h>

#include <map>
#include <string>

#include "statistics.h"

#define WARNING_FILE_NAME           "DO_NOT_MODIFY_THIS_IS_AN_AUTOGENERATED_DIRECTORY"
#define POSIX_GARBAGE_DIR           "/.garbage"
#define POSIX_GARBAGE_FLAGGED_FILE  "/flagged"
#define POSIX_GC_STAT_FILES_REMOVED "Files removed"
#define POSIX_GC_STAT_BYTES_REMOVED "Bytes removed"

const unsigned kDirLevels = 2;
const unsigned kDigitsPerDirLevel = 2;

struct fs_traversal_posix_context {
  int num_threads;
  std::map<ino_t, bool> gc_flagged;
};

/**
 * INITIALIZATION FUNCTIONS
 */
void InitialFsOperations(struct fs_traversal_context *ctx);
void FinalizeFsOperations(struct fs_traversal_context *ctx);
void InitializeWarningFile(struct fs_traversal_context *ctx);

/**
 * UTIL FUNCTIONS
 */
std::string BuildPath(struct fs_traversal_context *ctx, const char *dir);
std::string BuildHiddenPath(struct fs_traversal_context *ctx,
                            const char *ident);
int PosixSetMeta(const char *path, const struct cvmfs_attr *stat_info,
                 bool set_permissions = true);

bool BackupMtimes(std::string path, struct utimbuf *mtimes);


#endif  // CVMFS_SHRINKWRAP_POSIX_HELPERS_H_
