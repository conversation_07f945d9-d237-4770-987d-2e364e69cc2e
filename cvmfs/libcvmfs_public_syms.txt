__x86.get_pc_thunk.ax
__x86.get_pc_thunk.bx
__x86.get_pc_thunk.cx
__x86.get_pc_thunk.di
__x86.get_pc_thunk.dx
__x86.get_pc_thunk.si
__i686.get_pc_thunk.bx
__i686.get_pc_thunk.cx
cvmfs_nc_attr
cvmfs_nc_attr_init
cvmfs_nc_attr_free
cvmfs_attr
cvmfs_attr_init
cvmfs_attr_free
cvmfs_options_init
cvmfs_options_init_v2
cvmfs_options_init_legacy
cvmfs_options_clone
cvmfs_options_clone_legacy
cvmfs_options_fini
cvmfs_options_set
cvmfs_options_parse
cvmfs_options_parse_default
cvmfs_options_unset
cvmfs_options_get
cvmfs_options_dump
cvmfs_options_free
cvmfs_enable_threaded
cvmfs_init
cvmfs_init_v2
cvmfs_fini
cvmfs_attach_repo
cvmfs_attach_repo_v2
cvmfs_adopt_options
cvmfs_detach_repo
cvmfs_get_revision
cvmfs_remount
cvmfs_set_log_fn
cvmfs_statistics_format
cvmfs_open
cvmfs_pread
cvmfs_close
cvmfs_readlink
cvmfs_stat
cvmfs_lstat
cvmfs_listdir
cvmfs_listdir_contents
cvmfs_listdir_stat
cvmfs_stat_attr
cvmfs_stat_nc
cvmfs_list_nc
cvmfs_list_free
