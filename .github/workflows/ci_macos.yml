name: CI_MacOS

on:
  push:
    branches: ['devel', 'cvmfs*']
  pull_request:
    branches: ['devel', 'cvmfs*']
  workflow_dispatch:
jobs:
  macos_build_and_test:
    name: "macos_build_and_test"
    runs-on: macos-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Debug output 
        run: |
          csrutil status
          sw_vers 
          brew install coreutils
          sudo ln -s /usr/local/bin/gtimeout /usr/local/bin/timeout
          sudo ln -s /opt/homebrew/bin/greadlink /usr/local/bin/greadlink
          which timeout
          which greadlink
          sudo mkdir /usr/local/include
          ls /usr/local
          
      - name: Install Fuse-t and dependencies
        run: |
          brew install python-setuptools
          wget -q https://github.com/macos-fuse-t/fuse-t/releases/download/1.0.44/fuse-t-macos-installer-1.0.44.pkg
          sudo installer -pkg fuse-t-macos-installer-1.0.44.pkg -target /
          ls /usr/local/include

      - name: Build CVMFS
        run: |
          cd ${GITHUB_WORKSPACE}
          mkdir -p build
          # TODO: figure out why include paths prefer this over externals
          sudo rm  /Library/Frameworks/Mono.framework/Headers/sqlite*
          ./ci/build_package.sh ${GITHUB_WORKSPACE} ${GITHUB_WORKSPACE}/build cvmfs

      - name: Setup firmlinks
        run: |
          # / is readonly on macos 11+ - do 'synthetic firmlink' to create /cvmfs
          sudo zsh -c 'echo -e "cvmfs\tUsers/Shared/cvmfs\n#comment\n" > /etc/synthetic.conf'
          sudo chown root:wheel /etc/synthetic.conf
          sudo chmod a+r /etc/synthetic.conf
          # apfs.util seems to return non-zero error codes also on success
          sudo /System/Library/Filesystems/apfs.fs/Contents/Resources/apfs.util -t || true

          

      - name: Install CVMFS
        run: |
          export CVMFS_PACKAGE=$(ls ${GITHUB_WORKSPACE}/build/pkg_result/cvmfs-*.pkg)
          sudo installer -pkg ${CVMFS_PACKAGE} -target /

      - name: Setup CVMFS
        run: |
          # / is readonly on macos 11+ - do 'synthetic firmlink' to create /cvmfs
          sudo zsh -c 'echo -e "cvmfs\tUsers/Shared/cvmfs\n#comment\n" > /etc/synthetic.conf'
          sudo chown root:wheel /etc/synthetic.conf
          sudo chmod a+r /etc/synthetic.conf
          # apfs.util seems to return non-zero error codes also on success
          sudo /System/Library/Filesystems/apfs.fs/Contents/Resources/apfs.util -t || true
          sudo cvmfs_config setup
          sudo cvmfs_config chksetup || exit 1

      - name: Run Tests
        run: |
          sudo launchctl limit maxfiles 65536 65536 
          export CVMFS_PLATFORM_NAME="macos"
          export CVMFS_TIMESTAMP=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
          export CVMFS_TEST_CLASS_NAME=ClientIntegrationTests
          export CVMFS_TEST_PROXY=DIRECT
          cd $GITHUB_WORKSPACE/test
          ./run.sh /tmp/cvmfs-client-test.log -o /tmp/cvmfs-client-test.xunit.xml \
            -s quick                                          \
            -x src/004-davinci                                \
            src/005-asetup                                    \
            src/006-buildkernel                               \
            src/007-testjobs                                  \
            src/008-default_domain                            \
            src/015-rebuild_on_crash                           \
            src/016-dnsunreachable                            \
            src/017-dnstimeout                                \
            src/024-reload-during-asetup                      \
            src/028-negativecache                             \
            src/039-reloadalarm                               \
            src/040-aliencache                                \
            src/045-oasis                                     \
            src/052-roundrobindns                             \
            src/050-configrepo                                \
            src/053-uuid                                      \
            src/055-ownership                                 \
            src/056-lowspeedlimit                             \
            src/057-parallelmakecache                         \
            src/059-fallbackproxy                             \
            src/060-hidexattrs                                \
            src/061-systemdnokill                             \
            src/069-systemremount                             \
            src/074-oom                                       \
            src/077-doublemount                               \
            src/078-offlinemode                               \
            src/081-shrinkwrap                                \
            src/082-shrinkwrap-cms                            \
            src/083-suid                                      \
            src/084-premounted                                \
            src/087-xattrs                                    \
            src/088-watchdog                                  \
            src/089-external_cache_plugin                     \
            src/090-talkchroot                                \
            src/091-defaultconfigrepo                         \
            src/091-talksocket                                \
            src/092-stat                                      \
            src/094-attachmount                               \
            src/095-fuser                                     \
            src/096-cancelreq                                 \
            src/097-statfs                                    \
            src/099-http_tracing                              \
            src/100-reload-switch-debug                       \
            src/102-reusefd                                   \
            src/103-reloadcachemgr                            \
            src/104-concurrent_mounts                         \
            src/105-streaming-cache                           \
            src/406-ducc-webhook-notifications                \
            --                                                \
            src/0*                                            \
            src/1*
        
        

      - name: Upload logs as artifact
        if: ${{ always () }}
        uses: actions/upload-artifact@v4
        with: 
          name: CVMFS test logs
          path: |
              /tmp/cvmfs-client-test.log
              /tmp/cvmfs-server-test.log

