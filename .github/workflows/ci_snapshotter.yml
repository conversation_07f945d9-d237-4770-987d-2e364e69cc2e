name: C<PERSON>_snapshotter

on:
  push:
      paths:
        - 'snapshotter/**'
      branches: ['devel', 'cvmfs*']
  pull_request:
      paths:
        - 'snapshotter/**'
      branches: ['devel', 'cvmfs*']

jobs:
  docker:
    name: "CI_snapshotter"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.x'
      - name: Build
        working-directory: ./snapshotter
        run: go build -v ./...
      - name: Check Formatting
        working-directory: ./snapshotter
        run: | 
           [ -z "$(go fmt ./...)" ]
      - name: Test with the Go CLI
        working-directory: ./snapshotter
        run: go test -v ./...
