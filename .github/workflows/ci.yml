name: CI

on:
  push:
      branches: ['devel', 'cvmfs*']
  pull_request:
      branches: ['devel', 'cvmfs*']

jobs:
  docker:
    name: "Cloudtests (dockerized, alma9)"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Build  CVMFS in Image
        run: |
          cd test/common/container
          docker compose up --build -d cvmfs-dev

      - name: Setup CVMFS
        run: |
          docker exec -u sftnight -t cvmfs-dev bash -c \
            "sudo cvmfs_config setup"

      - name: Run Tests
        run: |
          docker exec -u sftnight -t cvmfs-dev bash -c \
            "cd /home/<USER>/cvmfs/test/common/container && CVMFS_TEST_PROXY=DIRECT bash test.sh"

      - name: Archive logs
        if: ${{ always () }}
        run: |
          docker cp cvmfs-dev:/tmp/cvmfs-client-test.log /tmp/cvmfs-client-test.log
          docker cp cvmfs-dev:/tmp/cvmfs-server-test.log /tmp/cvmfs-server-test.log

      - name: Upload logs as artifact
        if: ${{ always () }}
        uses: actions/upload-artifact@v4
        with: 
          name: CVMFS test logs
          path: |
              /tmp/cvmfs-client-test.log
              /tmp/cvmfs-server-test.log

