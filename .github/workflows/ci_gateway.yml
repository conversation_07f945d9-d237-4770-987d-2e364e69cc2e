name: CI_gateway

on:
  push:
      paths:
        - 'gateway/**'
      branches: ['devel', 'cvmfs*']
  pull_request:
      paths:
        - 'gateway/**'
      branches: ['devel', 'cvmfs*']

jobs:
  docker:
    name: "CI_gateway"
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.24.x'
      - name: Build
        working-directory: ./gateway
        run: go build -v ./...
      - name: Check Formatting
        working-directory: ./gateway
        run: | 
           [ -z "$(go fmt ./...)" ]
      - name: Test with the Go CLI
        working-directory: ./gateway
        run: go test -v ./...
