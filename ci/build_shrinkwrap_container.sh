#!/bin/bash

set -e

die() {
  echo "$1"
  exit 1
}

[ "x$CVMFS_SOURCE_LOCATION" != x ] || die "CVMFS_SOURCE_LOCATION missing"
[ "x$CVMFS_BUILD_LOCATION" != x ] || die "CVMFS_BUILD_LOCATION missing"
[ "x$CVMFS_SHRINKWRAP_PACKAGE" != x ] || die "CVMFS_SHRINKWRAP_PACKAGE missing"
[ "x$CVMFS_SHRINKWRAP_CONTAINER_RELEASE" != x ] || die "CVMFS_SHRINKWRAP_CONTAINER_RELEASE missing"

cd $CVMFS_BUILD_LOCATION
make -f $CVMFS_SOURCE_LOCATION/ci/shrinkwrap/Makefile \
  CVMFS_SRC=$CVMFS_SOURCE_LOCATION \
  SHRINKWRAP_PACKAGE=$CVMFS_SHRINKWRAP_PACKAGE \
  IMAGE_RELEASE=$CVMFS_SHRINKWRAP_CONTAINER_RELEASE
